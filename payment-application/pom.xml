<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.scube.payment</groupId>
        <artifactId>PaymentService</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>payment-application</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!--sCube libraries-->
        <auditing.version>1.10.0</auditing.version>
        <auth-resource-server-library.version>1.8.0</auth-resource-server-library.version>
        <notification.client.version>1.0.0</notification.client.version>
        <scube-rabbitmq.version>1.4.0</scube-rabbitmq.version>
        <multi-tenant.version>1.6.2</multi-tenant.version>
        <scheduling-lib.version>1.4.1</scheduling-lib.version>
        <cdc.version>0.0.5</cdc.version>
        <ErrorLibrary.version>0.6.3</ErrorLibrary.version>
        <miscellaneousLibrary.version>1.6.1</miscellaneousLibrary.version>
        <configUtilsLibrary.version>1.4.1</configUtilsLibrary.version>
        <permission-generator.version>1.2.0</permission-generator.version>
        <ai-library.version>1.14.2</ai-library.version>

        <ai-client.version>1.8.1</ai-client.version>
        <auth-client.version>1.5.0</auth-client.version>
        <calculation-client.version>1.14.4</calculation-client.version>
        <config-client.version>1.4.0</config-client.version>
        <document-client.version>1.7.0</document-client.version>
        <documenttemplates-client.version>1.5.3</documenttemplates-client.version>
        <document-template-helper-client.version>1.2.0</document-template-helper-client.version>
        <ocr-client.version>1.2.0</ocr-client.version>
        <imageprocessing-client.version>1.5.0</imageprocessing-client.version>
        <license-client.version>1.14.0</license-client.version>
        <notification-client.version>1.3.0</notification-client.version>
        <payment-client.version>1.9.2</payment-client.version>
        <report-client.version>1.3.1</report-client.version>
        <stripe-java.version>28.0.0</stripe-java.version>
        <gson.version>2.10.1</gson.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jersey</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <version>1.18.32</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stripe</groupId>
            <artifactId>stripe-java</artifactId>
            <version>${stripe-java.version}</version>
        </dependency>
        <!-- Required for Stripe -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>

        <!-- Schema versioning -->
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <version>${liquibase.version}</version>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-maven-plugin</artifactId>
            <version>${liquibase.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.blagerweij</groupId>
            <artifactId>liquibase-sessionlock</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.c4-soft.springaddons</groupId>
            <artifactId>spring-addons-starter-oidc-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.testcontainers/junit-jupiter -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-testcontainers -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-testcontainers</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>rabbitmq</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.testcontainers/postgresql -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.tngtech.archunit/archunit-junit5 -->
        <dependency>
            <groupId>com.tngtech.archunit</groupId>
            <artifactId>archunit-junit5</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-api</artifactId>
        </dependency>

        <!-- sCube Libraries -->
        <dependency>
            <groupId>com.scube.rabbit</groupId>
            <artifactId>rabbitmq-core</artifactId>
            <version>${scube-rabbitmq.version}</version>
        </dependency>
        <dependency>
            <groupId>com.scube.lib.error</groupId>
            <artifactId>ErrorLibrary</artifactId>
            <version>${ErrorLibrary.version}</version>
        </dependency>
        <dependency>
            <groupId>com.scube.lib.misc</groupId>
            <artifactId>utils</artifactId>
            <version>${miscellaneousLibrary.version}</version>
        </dependency>
        <dependency>
            <groupId>com.scube.multi.tenant</groupId>
            <artifactId>multi-tenancy</artifactId>
            <version>${multi-tenant.version}</version>
        </dependency>
        <dependency>
            <groupId>com.scube.config_utils</groupId>
            <artifactId>config-utils</artifactId>
            <version>${configUtilsLibrary.version}</version>
        </dependency>
        <dependency>
            <groupId>com.scube.config_utils</groupId>
            <artifactId>config-utils-test</artifactId>
            <version>${configUtilsLibrary.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.scube.auth.library</groupId>
            <artifactId>auth-core</artifactId>
            <version>${auth-resource-server-library.version}</version>
        </dependency>
        <dependency>
            <groupId>com.scube.multi.tenant</groupId>
            <artifactId>multi-tenancy-test</artifactId>
            <version>${multi-tenant.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.scube.lib.misc</groupId>
            <artifactId>validation-tests</artifactId>
            <version>${miscellaneousLibrary.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.scube.lib.misc</groupId>
            <artifactId>archunit-test</artifactId>
            <version>${miscellaneousLibrary.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.scube.lib.misc</groupId>
            <artifactId>annotation</artifactId>
            <version>${miscellaneousLibrary.version}</version>
        </dependency>
        <dependency>
            <groupId>com.scube.audit</groupId>
            <artifactId>audit-core</artifactId>
            <version>${auditing.version}</version>
        </dependency>
        <dependency>
            <groupId>com.scube.scheduling.lib</groupId>
            <artifactId>scheduling-core</artifactId>
            <version>${scheduling-lib.version}</version>
        </dependency>
        <dependency>
            <groupId>com.scube.scheduling.lib</groupId>
            <artifactId>scheduling-lock</artifactId>
            <version>${scheduling-lib.version}</version>
        </dependency>
        <dependency>
            <groupId>com.scube.client</groupId>
            <artifactId>http-exchange-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.scube.calculation</groupId>
            <artifactId>calculation-client</artifactId>
            <version>${calculation-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.scube.auth</groupId>
            <artifactId>auth-client</artifactId>
            <version>${auth-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.scube.config</groupId>
            <artifactId>config-client</artifactId>
            <version>${config-client.version}</version>
        </dependency>
    </dependencies>



    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.scube.payment.PaymentServiceApplication</mainClass>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-maven-plugin</artifactId>
                <version>${liquibase.version}</version>
                <configuration>
                    <promptOnNonLocalDatabase>true</promptOnNonLocalDatabase>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.postgresql</groupId>
                        <artifactId>postgresql</artifactId>
                        <version>${postgresql.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.12.1</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.32</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.6.0.Beta1</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                        <path>
                            <groupId>com.scube.client</groupId>
                            <artifactId>http-exchange-generator</artifactId>
                        </path>
                        <path>
                            <groupId>com.scube.permission</groupId>
                            <artifactId>permission-generator</artifactId>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
