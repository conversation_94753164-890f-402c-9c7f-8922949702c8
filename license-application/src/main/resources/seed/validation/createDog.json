[{"tableName": "participant", "name": "<PERSON><PERSON><PERSON>", "type": "string", "notNull": true, "max": 100}, {"tableName": "participant", "name": "tagNumber", "type": "string", "max": 50, "min": 1}, {"tableName": "participant", "name": "dogBreed", "type": "string", "max": 100, "min": 1}, {"tableName": "participant", "name": "dogBirthDate", "type": "date", "past": true, "ageMin": 0, "ageMax": 32}, {"tableName": "participant", "name": "dogSex", "type": "string", "max": 50}, {"tableName": "participant", "name": "dogSpayedOrNeutered", "type": "string", "max": 50}, {"tableName": "participant", "name": "dogPrimaryColor", "type": "string", "max": 100}, {"tableName": "participant", "name": "dogSecondaryColor", "type": "string", "max": 100}, {"tableName": "participant", "name": "licenseExempt", "type": "boolean"}, {"tableName": "participant", "name": "serviceDogType", "type": "string", "max": 50}, {"tableName": "participant", "name": "dogBio", "type": "string"}, {"tableName": "participant", "name": "dogMarkings", "type": "string", "max": 100}, {"tableName": "participant", "name": "cat<PERSON>riendly", "type": "string", "max": 50}, {"tableName": "participant", "name": "dogFriendly", "type": "string", "max": 50}, {"tableName": "participant", "name": "childFriendly", "type": "string", "max": 50}, {"tableName": "participant", "name": "veterinaryName", "type": "string", "max": 100}, {"tableName": "participant", "name": "veterinarianName", "type": "string", "max": 100}, {"tableName": "participant", "name": "vaccineDatesExempt", "type": "boolean"}, {"tableName": "participant", "name": "rabiesTagNumber", "type": "string", "max": 100}, {"tableName": "participant", "name": "vaccineName", "type": "string", "max": 100}, {"tableName": "participant", "name": "vaccineProducer", "type": "string", "max": 100}, {"tableName": "participant", "name": "vaccineBrand", "type": "string", "max": 100}, {"tableName": "participant", "name": "vaccineAdministeredDate", "type": "date", "pastOrPresent": true}, {"tableName": "participant", "name": "vaccineDueDate", "type": "date", "future": true}, {"tableName": "participant", "name": "vaccineLotNumber", "type": "string", "max": 100}, {"tableName": "participant", "name": "vaccineLotExpirationDate", "type": "date"}]