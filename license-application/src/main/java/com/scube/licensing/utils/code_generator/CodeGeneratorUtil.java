package com.scube.licensing.utils.code_generator;

import jakarta.validation.constraints.Size;
import lombok.experimental.UtilityClass;
import org.springframework.lang.NonNull;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Random;

import static com.scube.licensing.utils.code_generator.CGOptions.normalize;

@UtilityClass
public class CodeGeneratorUtil {
    @NonNull
    public static synchronized String generateCode(@Size(min = 1, max = 30) int length, CodeGeneratorExist codeGeneratorExist, CGOptions... options) {
        var code = generateCode(length, options);
        while (codeGeneratorExist.exists(code)) {
            code = generateCode(length, options);
        }
        return code;
    }

    @NonNull
    public static String generateCode(@Size(min = 1, max = 30) int length, CGOptions... options) {
        var allowedCharacters = determineAllowedCharacters(options);
        var shuffledCharacters = randomize(allowedCharacters);
        return shuffledCharacters.substring(0, length);
    }

    public static String randomize(String allowedCharacters) {
        var random = new Random(System.currentTimeMillis());
        var characters = allowedCharacters.toCharArray();
        for (int i = 0; i < characters.length; i++) {
            var randomIndex = random.nextInt(characters.length);
            swap(characters, i, randomIndex);
        }
        return new String(characters);
    }

    public static void swap(char[] characters, int i, int randomIndex) {
        var temp = characters[i];
        characters[i] = characters[randomIndex];
        characters[randomIndex] = temp;
    }

    public static String determineAllowedCharacters(CGOptions... options) {
        String alphabet = "ABCDEFGHJKMNPQRTUVWXYZ"; // without ambiguous characters
        String numbers = "23456789"; // without ambiguous characters

        StringBuilder allowedCharacters = new StringBuilder();

        if (ObjectUtils.isEmpty(options)) {
            options = new CGOptions[]{CGOptions.LOWER_UPPER_ALPHABET_NUMBERS};
        }
        List<CGOptions> optionsSet = normalize(options);

        for (var option : optionsSet) {
            switch (option) {
                case LOWER_ALPHABET -> allowedCharacters.append(alphabet.toLowerCase());
                case UPPER_ALPHABET -> allowedCharacters.append(alphabet.toUpperCase());
                case NUMBERS -> allowedCharacters.append(numbers);
                default -> throw new IllegalArgumentException("Unknown option: " + option);
            }
        }
        return allowedCharacters.toString();
    }

    public interface CodeGeneratorExist {
        boolean exists(String code);
    }
}