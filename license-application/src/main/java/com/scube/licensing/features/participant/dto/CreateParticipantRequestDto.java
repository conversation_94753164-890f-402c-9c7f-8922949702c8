package com.scube.licensing.features.participant.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.licensing.features.entity.EntityRequest;
import com.scube.licensing.features.entity.dtos.CreateAddressRequestDto;
import com.scube.licensing.features.entity.dtos.CreateCustomFieldsDto;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@EqualsAndHashCode(callSuper = true)
public class CreateParticipantRequestDto extends EntityRequest<CreateParticipantRequestDto> {
    public CreateParticipantRequestDto(Map<String, String> map) {
        super(map, Participant.TABLE_NAME);
    }

    @JsonProperty("title")
    public String getTitle() {
        return getAndValidate("title");
    }

    @JsonProperty("firstName")
    public String getFirstName() {
        return getAndValidate("firstName");
    }

    @JsonProperty("middleName")
    public String getMiddleName() {
        return getAndValidate("middleName");
    }

    @JsonProperty("lastName")
    public String getLastName() {
        return getAndValidate("lastName");
    }

    @JsonProperty("dateOfBirth")
    public String getDateOfBirth() {
        return getAndValidate("dateOfBirth");
    }

    @JsonProperty("suffix")
    public String getSuffix() {
        return getAndValidate("suffix");
    }

    @JsonProperty("email")
    public String getEmail() {
        return getAndValidate("email");
    }

    @JsonProperty("phone")
    public String getHomePhone() {
        return getAndValidate("phone");
    }

    @JsonProperty("workPhone")
    public String getWorkPhone() {
        return getAndValidate("workPhone");
    }

    @JsonProperty("fax")
    public String getFaxPhone() {
        return getAndValidate("fax");
    }

    @JsonProperty("cell")
    public String getCellPhone() {
        return getAndValidate("cell");
    }

    @JsonProperty("otherPhone")
    public String getOtherPhone() {
        return getAndValidate("otherPhone");
    }

    @JsonProperty("address")
    public String getAddress() {
        return getAndValidate("address");
    }

    @JsonProperty("address2")
    public String getAddress2() {
        return getAndValidate("address2");
    }

    @JsonProperty("city")
    public String getCity() {
        return getAndValidate("city");
    }

    @JsonProperty("state")
    public String getState() {
        return getAndValidate("state");
    }

    @JsonProperty("zip")
    public String getZip() {
        return getAndValidate("zip");
    }

    @JsonProperty("mailingSameAsPrimary")
    public boolean isMailingSameAsPrimary() {
        var mailSame = getAndValidate("mailingSameAsPrimary");
        return Boolean.parseBoolean(mailSame);
    }

    @JsonProperty("mailAddress")
    public String getMailAddress() {
        var mail = getAndValidate("mailAddress");
        if (isMailingSameAsPrimary()) return getAddress();
        return mail;
    }

    @JsonProperty("mailAddress2")
    public String getMailAddress2() {
        var mail = getAndValidate("mailAddress2");
        if (isMailingSameAsPrimary()) return getAddress2();
        return mail;
    }

    @JsonProperty("mailCity")
    public String getMailCity() {
        var mail = getAndValidate("mailCity");
        if (isMailingSameAsPrimary()) return getCity();
        return mail;
    }

    @JsonProperty("mailState")
    public String getMailState() {
        var mail = getAndValidate("mailState");
        if (isMailingSameAsPrimary()) return getState();
        return mail;
    }

    @JsonProperty("mailZip")
    public String getMailZip() {
        var mail = getAndValidate("mailZip");
        if (isMailingSameAsPrimary()) return getZip();
        return mail;
    }

    @JsonProperty("workAddress")
    public String getWorkAddress() {
        return getAndValidate("workAddress");
    }

    @JsonProperty("workAddress2")
    public String getWorkAddress2() {
        return getAndValidate("workAddress2");
    }

    @JsonProperty("workCity")
    public String getWorkCity() {
        return getAndValidate("workCity");
    }

    @JsonProperty("workState")
    public String getWorkState() {
        return getAndValidate("workState");
    }

    @JsonProperty("workZip")
    public String getWorkZip() {
        return getAndValidate("workZip");
    }

    @JsonProperty("registrationCode")
    public String getRegistrationCode() {
        return getAndValidate("registrationCode");
    }

    public boolean isRegistrationCodePresent() {
        return !ObjectUtils.isEmpty(getRegistrationCode());
    }

    public Map<String, Boolean> getOptIns() {
        return super.entrySet().stream()
                .filter(e -> e.getKey().toLowerCase().startsWith("optin"))
                .filter(e -> !ObjectUtils.isEmpty(e.getValue()))
                .collect(HashMap::new, (m, e) -> m.put(e.getKey(), Boolean.parseBoolean((String) e.getValue())), HashMap::putAll);
    }

    public List<CreateContactRequestDto> getContacts() {
        return List.of(
                CreateContactRequestDto.createPhone("Home", getHomePhone()),
                CreateContactRequestDto.createPhone("Work", getWorkPhone()),
                CreateContactRequestDto.createPhone("Fax", getFaxPhone()),
                CreateContactRequestDto.createPhone("Cell", getCellPhone()),
                CreateContactRequestDto.createPhone("Other", getOtherPhone()),

                CreateContactRequestDto.createEmail("Primary", getEmail())
        );
    }

    public Map<String, String> getContactsToUpdate() {
        return super.entrySet().stream()
                .filter(e -> e.getKey().startsWith("contact"))
                .collect(HashMap::new, (m, e) -> m.put(e.getKey().replace("contact", ""), (String) e.getValue()), HashMap::putAll);
    }

    public Map<String, Map<String, String>> getAddressesToUpdate() {
        var addresses = super.entrySet().stream()
                .filter(e -> e.getKey().startsWith("address")).toList();
        var result = new HashMap<String, Map<String, String>>();
        for (Entry<String, Object> address : addresses) {
            var split = address.getKey().split("_");
            if (split.length != 3) {
                log.error("Invalid address key format: {}", address.getKey());
                continue;
            }
            var addressIdStr = split[1];
            var addressField = split[2];
            var value = (String) address.getValue();
            result.computeIfAbsent(addressIdStr, k -> new HashMap<>()).put(addressField, value);
        }
        return result;
    }

    @Override
    public List<CreateAddressRequestDto> getAddresses() {
        return List.of(
                CreateAddressRequestDto.createAddress("Home", getAddress(), getAddress2(), getCity(), getState(), getZip()),
                CreateAddressRequestDto.createAddress("Mailing", getMailAddress(), getMailAddress2(), getMailCity(), getMailState(), getMailZip()),
                CreateAddressRequestDto.createAddress("Work", getWorkAddress(), getWorkAddress2(), getWorkCity(), getWorkState(), getWorkZip())
        );
    }

    public List<CreateCustomFieldsDto> getCustomFields() {
        var result = new ArrayList<>(List.of(
                CreateCustomFieldsDto.createCustomField("string", Participant.TABLE_NAME, "title", getTitle()),
                CreateCustomFieldsDto.createCustomField("string", Participant.TABLE_NAME, "firstName", getFirstName()),
                CreateCustomFieldsDto.createCustomField("string", Participant.TABLE_NAME, "middleName", getMiddleName()),
                CreateCustomFieldsDto.createCustomField("string", Participant.TABLE_NAME, "lastName", getLastName()),
                CreateCustomFieldsDto.createCustomField("date", Participant.TABLE_NAME, "dateOfBirth", getDateOfBirth()),
                CreateCustomFieldsDto.createCustomField("string", Participant.TABLE_NAME, "suffix", getSuffix())
        ));

        var excludedOptIn = getOptIns().keySet();
        getAdditionalFields(excludedOptIn)
                .forEach((key, value) -> result.add(CreateCustomFieldsDto.createCustomField("object", Participant.TABLE_NAME, key, value)));

        return result;
    }
}