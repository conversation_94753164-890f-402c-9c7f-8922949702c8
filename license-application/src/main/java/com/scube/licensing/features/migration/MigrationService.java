package com.scube.licensing.features.migration;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;

@Service
@RequiredArgsConstructor
@Transactional
public class MigrationService {
    private final JdbcTemplate jdbcTemplate;
    @Value("${spring.datasource.username:yourusername}")
    private String dbUsername;

    @Value("${spring.datasource.password:yourpassword}")
    private String dbPassword;

    private static final Set<String> TENANTS = Set.of("chazy", "schenectady");

    @Transactional
    public void migrateCodeLookup(String sourceTenant) {
        if (!TENANTS.contains(sourceTenant)) {
            throw new IllegalArgumentException("Invalid tenant");
        }
        String hostSql = "SELECT inet_server_addr()";
        String host = jdbcTemplate.queryForObject(hostSql, String.class);

        String sql = String.format("""
                -- Step 1: Install the postgres_fdw extension
                CREATE EXTENSION IF NOT EXISTS postgres_fdw;
                
                -- Step 2: Create foreign server
                DROP SERVER IF EXISTS source_server CASCADE;
                CREATE SERVER source_server
                FOREIGN DATA WRAPPER postgres_fdw
                OPTIONS (host '%s', port '5432', dbname '%s');
                
                -- Step 3: Create foreign table pointing to the source table
                CREATE FOREIGN TABLE code_lookup_foreign (
                    code_lookup_id BIGINT,
                    conversion_reference CHARACTER VARYING(255),
                    created_by CHARACTER VARYING(255),
                    created_date TIMESTAMP WITHOUT TIME ZONE,
                    last_modified_by CHARACTER VARYING(255),
                    last_modified_date TIMESTAMP WITHOUT TIME ZONE,
                    properties JSONB,
                    code_lookup_uuid UUID,
                    code CHARACTER VARYING(30),
                    entity_type CHARACTER VARYING(255),
                    entity_id CHARACTER VARYING(255),
                    realm CHARACTER VARYING(255),
                    action CHARACTER VARYING(255)
                )
                SERVER source_server
                OPTIONS (schema_name 'license', table_name 'code_lookup');
                
                DROP USER MAPPING IF EXISTS FOR CURRENT_USER SERVER source_server;
                CREATE USER MAPPING FOR CURRENT_USER
                SERVER source_server
                OPTIONS (user '%s', password '%s');
                
                -- Step 4: Insert data from foreign table to target table
                INSERT INTO license.code_lookup (
                    conversion_reference,
                    created_by,
                    created_date,
                    last_modified_by,
                    last_modified_date,
                    properties,
                    code_lookup_uuid,
                    code,
                    entity_type,
                    entity_id,
                    realm,
                    action
                )
                SELECT
                    conversion_reference,
                    created_by,
                    created_date,
                    last_modified_by,
                    last_modified_date,
                    properties,
                    code_lookup_uuid,
                    code,
                    entity_type,
                    entity_id,
                    '%s' AS realm,
                    action
                FROM code_lookup_foreign;
                
                -- Step 5: Clean up (optional)
                    DROP FOREIGN TABLE code_lookup_foreign;
                    DROP USER MAPPING FOR CURRENT_USER SERVER source_server;
                    DROP SERVER source_server;
                """, host, sourceTenant, dbUsername, dbPassword, sourceTenant);
        jdbcTemplate.execute(sql);

    }


    @Transactional
    public void migrateCodeLookupToAllTenant() {
        for (String tenant : TENANTS) {
            migrateCodeLookup(tenant);
        }
    }
}
