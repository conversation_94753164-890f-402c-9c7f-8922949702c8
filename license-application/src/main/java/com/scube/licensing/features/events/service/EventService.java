package com.scube.licensing.features.events.service;

import com.scube.licensing.features.events.Event;
import com.scube.licensing.features.profile.dto.EventDto;
import com.scube.licensing.infrastructure.db.entity.event.EventType;
import com.scube.licensing.infrastructure.db.repository.event.EventTypeRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
@Transactional
public class EventService {
    private final EventTypeRepository eventTypeRepository;

    public List<EventDto> toEventDtos(List<Event> events) {
        log.debug("EventService.getEventTypeFields()");

        if (events == null || events.isEmpty()) {
            return List.of();
        }

        //Get unique event type ids
        Set<Long> eventTypeIds = events.stream()
                .map(Event::getEventTypeId)
                .collect(Collectors.toSet());

        //Retrieve event types
        Map<Long, EventType> eventTypes = eventTypeRepository.findAllById(eventTypeIds)
                .stream()
                .collect(Collectors.toMap(EventType::getId, Function.identity()));

        return events.stream().map(event -> {
            EventType eventType = eventTypes.get(event.getEventTypeId());

            return new EventDto(eventType, event);
        }).toList();
    }
}
