package com.scube.licensing.features.license.type;


import com.scube.licensing.infrastructure.db.entity.license.type.LicenseType;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public final class LicenseTypeResponse {

    private Long id;
    private String code;
    private String name;
    private String description;
    private String groupName;

    public static LicenseTypeResponse from(LicenseType licenseType) {
        return LicenseTypeResponse.builder()
                .id(licenseType.getId())
                .code(licenseType.getCode())
                .name(licenseType.getName())
                .description(licenseType.getDescription())
                .groupName(licenseType.getGroupName())
                .build();
    }
}
