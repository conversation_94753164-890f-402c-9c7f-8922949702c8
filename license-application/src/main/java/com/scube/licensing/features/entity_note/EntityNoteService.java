package com.scube.licensing.features.entity_note;

import com.scube.audit.auditable.services.AuditableEntityService;
import com.scube.licensing.features.document.service.DocumentService;
import com.scube.licensing.features.entity.AssociableService;
import com.scube.licensing.features.entity_note.dtos.EntityNoteDto;
import com.scube.licensing.features.entity_note.dtos.EntityNoteRequest;
import com.scube.licensing.features.entity_note.mapper.EntityNoteDtoMapper;
import com.scube.licensing.infrastructure.db.entity.entity_note.EntityNote;
import com.scube.licensing.infrastructure.db.repository.entity_note.EntityNoteRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class EntityNoteService extends AuditableEntityService<Long, EntityNote, EntityNoteRepository> {
    private final AssociableService associableService;
    private final DocumentService documentService;
    private final EntityNoteDtoMapper mapper;

    public EntityNoteDto createNote(Map<String, String> fields, Map<String, MultipartFile> files) {
        log.info("Start create...");
        files = documentService.processFieldToBeDeleted(fields, files);
        var requestDto = new EntityNoteRequest(fields);
        var entityNote = new EntityNote();
        entityNote.setNote(requestDto.getNote());
        associableService.upsertAssociable(entityNote, requestDto);
        repository.save(entityNote);
        documentService.upload(entityNote, files);
        var result = mapper.toDto(entityNote);
        log.info("End create...");
        return result;
    }

    public EntityNoteDto updateNote(EntityNote entityNote, Map<String, String> fields, Map<String, MultipartFile> files) {
        log.info("Start update...");
        files = documentService.processFieldToBeDeleted(fields, files);
        var requestDto = new EntityNoteRequest(fields);
        entityNote.setNote(requestDto.getNote());
        associableService.upsertAssociable(entityNote, requestDto);
        repository.save(entityNote);
        documentService.upload(entityNote, files);
        var result = mapper.toDto(entityNote);
        log.info("End update...");
        return result;
    }

    public void deleteNote(EntityNote entityNote) {
        log.info("Start delete...");
        associableService.deleteEntityAssociations(entityNote);
        repository.delete(entityNote);
        log.info("End delete...");
    }
}