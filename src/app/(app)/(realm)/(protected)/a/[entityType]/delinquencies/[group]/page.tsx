"use client";

import React, { useEffect, useMemo, useState } from "react";
import {
  useReactTable,
  getCoreRowModel,
  getGroupedRowModel,
  getExpandedRowModel,
  flexRender,
  ColumnDef,
} from "@tanstack/react-table";
import { useGetLicenses2 } from "@/hooks/api/useLicense";
import { useAtom } from "jotai";
import { toast<PERSON>tom } from "@/components/ui/toast/toast";
import { format, startOfMonth, endOfMonth } from "date-fns";
import { useSearchParams, useRouter } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { Address, Contact } from "@/types/IndividualType";
import { formatPhoneNumber } from "@/components/license/licenseHelper";
import { Dog } from "@/types/DogType";
import { useGenerateReport } from "@/hooks/api/useReport";
import { downloadList<PERSON>tom, modal<PERSON><PERSON><PERSON>tom, ReportType, updateDownloadList } from "@/components/reports/scripts/downloadCenterUtils";
import { Button } from "@/components/ui/button";

interface License {
  entityId: string;
  licenseNumber: string;
  validToDate: string;
  status: string;
}

interface Individual {
  entityId: string;
  firstName: string;
  lastName: string;
  addresses: Address[];
  contacts: Contact[];
}

interface LicenseProp {
  license: License;
  individual: Individual[];
  dog: Dog[];
}

const months = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const DelinquencyGroup = () => {
  const [_, setToast] = useAtom(toastAtom);
  const searchParams = useSearchParams();
  const router = useRouter();

  const defaultMonth = new Date().getMonth().toString();
  const defaultYear = new Date().getFullYear().toString();

  const month = searchParams.get("month") || defaultMonth;
  const year = searchParams.get("year") || defaultYear;
  const pageParam = parseInt(searchParams.get("page") || "1", 10);
  const sizeParam = parseInt(searchParams.get("size") || "50", 10);
  const selectedFormat = searchParams.get("selectedFormat") || "pdf";

  const [page, setPage] = useState(pageParam);
  const [size, setSize] = useState(sizeParam);

  const { data, isLoading, isError, error } = useGetLicenses2(
    {
      size: size.toString(),
      page: (page - 1).toString(),
      status: "expired",
      validToDate: `${format(
        startOfMonth(new Date(Number(year), Number(month))),
        "yyyy-MM-dd",
      )},${format(
        endOfMonth(new Date(Number(year), Number(month))),
        "yyyy-MM-dd",
      )}`,
    },
    Boolean(month && year),
  );

  const [licenses, setLicenses] = useState<LicenseProp[] | null>(null);
  const [reportUuid, setReportUuid] = useState<string | null>(null);
  const [downloadList, setDownloadList] = useAtom(downloadListAtom);
  const [modalOpen, setModalOpen] = useAtom(modalOpenAtom);

  const generateReport = useGenerateReport();

  useEffect(() => {
    if (data && licenses !== data?.content) {
      setLicenses(data?.content);
    }
  }, [data, licenses]);

  const totalDelinquencies = data?.totalElements || 0;

  const updateSearchParams = (key: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set(key, value);
    router.push(`?${params.toString()}`);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    updateSearchParams("page", newPage.toString());
  };

  const handleSizeChange = (newSize: number) => {
    setSize(newSize);
    updateSearchParams("size", newSize.toString());
  };

  const handleDownloadReport = (reportId: string, title: string) => {
    const requestData = {
      reportTypeId: reportId ?? "",
      body: {
        startDate: format(
          startOfMonth(new Date(Number(year), Number(month))),
          "yyyy-MM-dd",
        ),
        endDate: format(
          endOfMonth(new Date(Number(year), Number(month))),
          "yyyy-MM-dd",
        ),
      },
      selectedFormat,
    };

    generateReport.mutate(requestData, {
      onSuccess: (response) => {
        if (response?.uuid) {
          setReportUuid(response.uuid);

          const newReport: ReportType = {
            reportId: response.uuid,
            documentId: response.uuid,
            title,
            status: "PENDING",
            createdBy: "currentUserId",
            completedDate: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          const updatedList = updateDownloadList(downloadList, newReport);
          setDownloadList(updatedList);
          setModalOpen(true);
        } else {
          setToast({
            message: "Failed to generate report.",
            label: "Error",
            status: "error",
          });
        }
      },
      onError: () => {
        setToast({
          message: "Error generating report.",
          label: "Error",
          status: "error",
        });
      },
    });
  };

  if (isLoading) {
    return <Loader message="Loading delinquent licenses..." />;
  }

  if (isError) {
    setToast({
      message: "Error fetching licenses",
      label: "Error",
      status: "error",
    });
    return <ErrorMessage message="Error fetching licenses" />;
  }

  const groupedData = licenses
    ? groupLicensesByIndividual(licenses).sort((a, b) =>
        a.lastName.localeCompare(b.lastName),
      )
    : [];

  return (
    <div className="flex h-full w-full flex-col gap-6 overflow-auto">
      <div className="container mx-auto flex flex-col gap-4 py-6">
        <YearSelector
          year={year}
          setYear={(newYear) => updateSearchParams("year", newYear)}
        />
        <MonthSelector
          month={month}
          setMonth={(newMonth) => updateSearchParams("month", newMonth)}
        />

        <hr />

        <div className="flex justify-between gap-10">
          <div
            className={cn(
              "py-4 text-center text-xl font-semibold",
              totalDelinquencies > 0 ? "text-red-500" : "text-blue-500",
            )}
          >
            Total Delinquencies: {totalDelinquencies}
          </div>

          {totalDelinquencies > 0 && (
            <div className="flex items-center gap-5">
              <Button
                onClick={() =>
                  handleDownloadReport(
                    "e4eb312f-9d10-49d7-bec7-9e95f185c9ac",
                    `Delinquent Dog Report ${months[Number(month)]} ${year}`,
                  )
                }
              >
                Print Report
              </Button>
              <Button
                onClick={() =>
                  handleDownloadReport(
                    "18a3db7e-e34e-427e-84da-6ff7c08ed4b9",
                    `Delinquent Dog Notices ${months[Number(month)]} ${year}`,
                  )
                }
                variant={"outline"}
              >
                Print Notices
              </Button>
            </div>
          )}
        </div>

        {groupedData.length > 0 ? (
          <>
            <LicensesTable data={groupedData} />
            <Pagination
              page={page}
              size={size}
              total={data?.totalPages || 1}
              onPageChange={handlePageChange}
              onSizeChange={handleSizeChange}
            />
          </>
        ) : (
          <div className="flex flex-col gap-2 text-center text-lg text-gray-600">
            No delinquent licenses found.
            <Image
              src="/images/resident/doghappy.png"
              alt="Happy Dog"
              width={200}
              height={200}
              className="mx-auto"
            />
          </div>
        )}
      </div>
    </div>
  );
};


const groupLicensesByIndividual = (licenses: LicenseProp[]): any[] => {
  const grouped = licenses.reduce<Record<string, LicenseProp[]>>(
    (acc, item) => {
      const individualId = item.individual[0]?.entityId || "Unknown";
      acc[individualId] = acc[individualId] || [];
      acc[individualId].push(item);
      return acc;
    },
    {},
  );

  return Object.entries(grouped).map(([id, licenses]) => {
    const firstIndividual = licenses[0].individual[0] as Individual;

    return {
      firstName: firstIndividual?.firstName ?? "N/A",
      lastName: firstIndividual?.lastName ?? "N/A",
      addresses: firstIndividual?.addresses ?? [],
      contacts: firstIndividual?.contacts ?? [],
      licenses,
    };
  });
};

const LicensesTable = ({ data }: { data: any[] }) => {
  const columns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        accessorKey: "owner",
        header: "Owner Information",
        cell: (info) => {
          const owner = info.row.original;
          const address = owner.addresses?.find(
            (addr: Address) => addr.participantAddressType === "Home",
          );
          const primaryContact = owner.contacts?.find(
            (contact: Contact) =>
              contact.group === "Home" && contact.type === "Phone",
          );

          return (
            <div className="flex flex-col">
              {/* Owner Name */}
              <span className="font-semibold">
                {owner.firstName} {owner.lastName}
              </span>
              {/* Address */}
              {address ? (
                <div className="text-sm text-gray-600">
                  <span>{address.streetAddress}</span>
                  {address.streetAddress2 && (
                    <span>, {address.streetAddress2}</span>
                  )}
                  <span>
                    , {address.city}, {address.state} {address.zip}
                  </span>
                </div>
              ) : (
                <span className="text-sm text-gray-500">
                  No address available
                </span>
              )}
              {/* Phone */}
              {primaryContact ? (
                <span className="text-sm text-gray-600">
                  {formatPhoneNumber(primaryContact.value)}
                </span>
              ) : (
                <span className="text-sm text-gray-500">
                  No phone number available
                </span>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: "licenses",
        header: "Licenses",
        cell: ({ row }) => (
          <div className="ml-4 flex flex-col gap-2">
            {row.original.licenses.map((license: LicenseProp) => (
              <div key={license.license.entityId}>
                <Link
                  href={`/a/license/${license.license.entityId}`}
                  className="flex flex-col text-blue-500"
                  target="_blank"
                >
                  <span>
                    {license.license.licenseNumber} -{" "}
                    {license?.dog
                      ?.map((dog) => dog?.dogName ?? "N/A")
                      .join(", ")}
                  </span>
                  <div>
                    {/* Dog Breed */}
                    <span className="text-sm text-gray-600">
                      {license?.dog
                        ?.map((dog) => dog?.dogBreed ?? "N/A")
                        .join(", ")}
                    </span>
                  </div>
                  <small className="text-xs text-neutral-600">
                    (License Expired:{" "}
                    {format(
                      new Date(license.license.validToDate),
                      "MM/dd/yyyy",
                    )}
                    )
                  </small>
                </Link>
              </div>
            ))}
          </div>
        ),
      },
    ],
    [],
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getGroupedRowModel: getGroupedRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
  });

  return <Table table={table} />;
};

const Table = ({ table }: { table: ReturnType<typeof useReactTable> }) => {
  return (
    <div className="overflow-x-auto">
      <table className="w-full table-auto border-collapse border border-gray-300">
        <thead className="bg-gray-100">
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  className="border border-gray-300 px-4 py-2 text-left font-semibold"
                >
                  {flexRender(
                    header.column.columnDef.header,
                    header.getContext(),
                  )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) => (
            <tr key={row.id} className="hover:bg-gray-50">
              {row.getVisibleCells().map((cell) => (
                <td
                  key={cell.id}
                  className="border border-gray-300 px-4 py-2 text-gray-700"
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

const YearSelector = ({
  year,
  setYear,
}: {
  year: string;
  setYear: (year: string) => void;
}) => (
  <div className="flex flex-wrap items-center justify-center gap-2">
    {Array.from({ length: 5 }, (_, i) => {
      const currentYear = new Date().getFullYear() - i;
      return (
        <button
          key={currentYear}
          onClick={() => setYear(currentYear.toString())}
          className={`${
            year === currentYear.toString()
              ? "bg-blue-500 text-white"
              : "bg-gray-200 text-gray-700"
          } rounded-md px-4 py-2`}
        >
          {currentYear}
        </button>
      );
    })}
  </div>
);

const MonthSelector = ({
  month,
  setMonth,
}: {
  month: string;
  setMonth: (month: string) => void;
}) => (
  <div className="flex flex-wrap justify-center gap-2">
    {months.map((monthName, index) => (
      <button
        key={index}
        onClick={() => setMonth(index.toString())}
        className={`${
          month === index.toString()
            ? "bg-blue-500 text-white"
            : "bg-gray-200 text-gray-700"
        } rounded-md px-4 py-2`}
      >
        {monthName}
      </button>
    ))}
  </div>
);

const Loader = ({ message }: { message: string }) => (
  <div className="flex h-full w-full animate-pulse items-center justify-center text-lg text-gray-600">
    {message}
  </div>
);

const ErrorMessage = ({ message }: { message: string }) => (
  <div className="flex h-full w-full items-center justify-center text-lg text-red-600">
    {message}
  </div>
);

export default DelinquencyGroup;


const Pagination = ({
  page,
  size,
  total,
  onPageChange,
  onSizeChange,
}: {
  page: number;
  size: number;
  total: number;
  onPageChange: (page: number) => void;
  onSizeChange: (size: number) => void;
}) => {
  return (
    <div className="flex items-center justify-between py-4">
      <div>
        <label>
          Page Size:
          <select
            value={size}
            onChange={(e) => onSizeChange(Number(e.target.value))}
            className="ml-2 p-2 border border-gray-300 rounded"
          >
            {[10, 20, 50].map((sizeOption) => (
              <option key={sizeOption} value={sizeOption}>
                {sizeOption}
              </option>
            ))}
          </select>
        </label>
      </div>
      <div className="flex items-center gap-2">
        <button
          onClick={() => onPageChange(page - 1)}
          disabled={page === 1}
          className="p-2 bg-gray-200 text-gray-700 rounded disabled:opacity-50"
        >
          Previous
        </button>
        <span>
          Page {page} of {total}
        </span>
        <button
          onClick={() => onPageChange(page + 1)}
          disabled={page === total}
          className="p-2 bg-gray-200 text-gray-700 rounded disabled:opacity-50"
        >
          Next
        </button>
      </div>
    </div>
  );
};