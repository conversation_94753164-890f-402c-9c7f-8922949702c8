package com.scube.coordinator.features.payment.services;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.coordinator.features.license.LicenseService;
import com.scube.coordinator.features.order.OrderService;
import com.scube.coordinator.features.payment.dto.*;
import com.scube.coordinator.features.payment.mapper.PaymentMapper;
import com.scube.licensing.features.profile.dto.gen_dto.ParticipantDto;
import com.scube.payment.features.payment.processing.dto.gen_dto.SubmitPaymentResponseDto;
import com.scube.payment.features.providers.gateway.gen_dto.PaymentTokenResponse;
import com.scube.rabbit.core.AmqpGateway;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.util.UUID;

import static com.scube.coordinator.constants.URIConstants.DOC_STORAGE_URI;

@Service
@Slf4j
@AllArgsConstructor
public class PaymentService {
    private final PaymentProcessingService paymentProcessingService;
    private final LicenseServiceConnection licenseServiceConnection;
    private final ReceiptService receiptService;
    private final OrderService orderService;
    private final PaymentMapper paymentMapper;
    private final LicenseService licenseService;
    private final AmqpGateway amqpGateway;

    public CoordinatorPaymentResponseDto submitPaymentByItems(CoordinatorSubmitPaymentRequestDto paymentRequest) {
        validatePaymentRequest(paymentRequest);
        OrderInvoiceResponse order = orderService.createOrderByItemsForPayment(paymentRequest);
        SubmitPaymentResponseDto paymentResponse = paymentProcessingService.submitPayment(paymentRequest, order, false);
        receiptService.generateReceipt(paymentRequest, paymentResponse.getPaymentId(), order);
        return new CoordinatorPaymentResponseDto(paymentResponse.getStatus(), paymentResponse.getOrderId(), paymentResponse.getBalance());
    }

    public CoordinatorPaymentResponseDto submitPaymentByCart(CoordinatorSubmitPaymentRequestByCartDto paymentRequest, boolean isMe) {
        validatePaymentRequest(paymentRequest);
        OrderInvoiceResponse order = orderService.createOrderForPayment(paymentRequest, isMe);
        SubmitPaymentResponseDto paymentResponse;

        try {
            paymentResponse = paymentProcessingService.submitPayment(paymentRequest, order, isMe);
        } catch (Throwable t) {
            log.error("Error submitting payment", t);
            orderService.rollbackOrderToCart(paymentRequest.getCartId(), order.getOrderId(), isMe);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error submitting payment");
        }

        receiptService.generateReceipt(paymentRequest, paymentResponse.getPaymentId(), order);
        amqpGateway.publish(new CalculationObserverEvent("remove", order.getItems().stream().map(OrderInvoiceItem::getItemId).toList()));

        return new CoordinatorPaymentResponseDto(paymentResponse.getStatus(), paymentResponse.getOrderId(), paymentResponse.getBalance());
    }

    public CoordinatorPaymentResponseDto submitPaymentByOrder(CoordinatorSubmitPaymentRequestByOrderDto paymentRequest) {
        validatePaymentRequest(paymentRequest);
        OrderInvoiceResponse order = orderService.getOrderForPayment(paymentRequest, false);
        SubmitPaymentResponseDto paymentResponse = paymentProcessingService.submitPayment(paymentRequest, order, false);
        receiptService.generateReceipt(paymentRequest, paymentResponse.getPaymentId(), order);
        return new CoordinatorPaymentResponseDto(paymentResponse.getStatus(), paymentResponse.getOrderId(), paymentResponse.getBalance());
    }

    public GetOrderReceiptsResponseDto getReceipts(UUID orderId, boolean isMe) {
        return new GetOrderReceiptsResponseDto(orderId,
                paymentProcessingService.getPayments(orderId, isMe)
                        .stream()
                        .filter(payment -> payment.getReceiptId() != null)
                        .map(payment -> GetOrderReceiptsResponseDto.ReceiptDto.builder()
                                .paymentId(payment.getTransactionId())
                                .transactionDate(payment.getTransactionDate())
                                .receiptUrl(DOC_STORAGE_URI + payment.getReceiptId())
                                .build())
                        .toList()
        );
    }

    public void generateReceipt(UUID orderId, boolean isMe) {
        OrderInvoiceResponse order = orderService.getOrder(orderId, isMe);
        var payments = paymentProcessingService.getPayments(orderId, isMe);
        payments.forEach(payment -> receiptService.generateReceipt(payment, order));
    }

    public static void validatePaymentRequest(CoordinatorSubmitPaymentRequestDto paymentRequest) {
        if (paymentRequest.getAmount().compareTo(BigDecimal.ZERO) <= 0 && !paymentRequest.getPaymentType().equalsIgnoreCase("cash")) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Please select cash as payment type for zero amount payment");
        }
    }

    public PaymentTokenResponse checkout(CheckoutDto checkoutRequest, boolean isMe) {
        //licenseService.checkHouseHoldRule(checkoutRequest.getCartId());

        OrderInvoiceResponse order = orderService.createOrderForPayment(checkoutRequest, isMe);

        PaymentTokenResponse resp;

        try {
            resp = paymentProcessingService.getHostedPaymentPageToken(order, checkoutRequest.getAmount(), isMe);
        } catch (Throwable t) {
            log.error("Error submitting payment", t);
            orderService.rollbackOrderToCart(checkoutRequest.getCartId(), order.getOrderId(), isMe);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error submitting payment");
        }

        return resp;
    }

    public CheckoutNoPaymentDueResponseDto checkout(CheckoutNoPaymentDueDto checkoutRequest, OpenidClaimSet jwt) {
        //licenseService.checkHouseHoldRule(checkoutRequest.getCartId());
        ParticipantDto participant = licenseServiceConnection.loggedInUserParticipant().getParticipant();

        CoordinatorSubmitPaymentRequestByCartDto paymentRequest =
                paymentMapper.toSubmitPaymentRequestByCartDto(
                        checkoutRequest, participant, jwt.getGivenName(), jwt.getFamilyName());

        CoordinatorPaymentResponseDto response = submitPaymentByCart(paymentRequest, true);

        return new CheckoutNoPaymentDueResponseDto(response.getOrderId());
    }
}
