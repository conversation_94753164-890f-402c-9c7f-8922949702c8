package com.scube.notification.db.seed.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * An annotation used for seeding data, allows control over order, skipping, and priority of seeding operations.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface Seed {
    /**
     * Specifies whether to skip the seeding operation.
     *
     * @return True if the seeding operation should be skipped, false otherwise.
     */
    boolean skip() default false;

    /**
     * Specifies the order in which the seeding operation should be executed.
     * Lower values have higher priority. So if two seeding operations have orders 1 and 2, the one with order 1 will be executed first.
     *
     * @return The order number of the seeding operation.
     */
    int priority();
}